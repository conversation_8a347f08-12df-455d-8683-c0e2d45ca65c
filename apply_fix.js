#!/usr/bin/env node
/**
 * <PERSON><PERSON><PERSON> to apply Smart Connections detection fix to MCP Tools plugin
 * This implements the fix from PR #23 for macOS systems
 */

const fs = require('fs');
const path = require('path');

// Path to the MCP Tools main.js file
const MCP_TOOLS_PATH = "/Users/<USER>/Library/CloudStorage/OneDrive-COOBLINKINC/obsidian-projects/.obsidian/plugins/mcp-tools/main.js";
const BACKUP_PATH = MCP_TOOLS_PATH + '.backup.' + Date.now();

/**
 * Apply the Smart Connections detection fix
 */
function applySmartConnectionsFix() {
    try {
        console.log("🔧 Starting Smart Connections detection fix...");
        
        // Read the original file
        if (!fs.existsSync(MCP_TOOLS_PATH)) {
            throw new Error(`MCP Tools main.js not found at: ${MCP_TOOLS_PATH}`);
        }
        
        const originalContent = fs.readFileSync(MCP_TOOLS_PATH, 'utf8');
        console.log(`📖 Read original file (${originalContent.length} characters)`);
        
        // Create backup
        fs.writeFileSync(BACKUP_PATH, originalContent);
        console.log(`💾 Created backup at: ${BACKUP_PATH}`);
        
        // Apply the fix - replace Smart Connections detection logic
        let fixedContent = originalContent;
        
        // Look for patterns that might be the Smart Connections detection
        const smartSearchPatterns = [
            /window\.SmartSearch/g,
            /window\["SmartSearch"\]/g,
            /SmartSearch\s*:/g,
        ];
        
        // Enhanced detection function based on PR #23
        const enhancedDetection = `
// Enhanced Smart Connections detection (PR #23 fix)
function detectSmartSearch() {
    // First try window.SmartSearch (works on some platforms)
    if (window.SmartSearch) {
        return window.SmartSearch;
    }
    
    // Fallback: app.plugins.plugins["smart-connections"].env (fixes macOS/Linux)
    try {
        if (window.app && window.app.plugins && window.app.plugins.plugins && 
            window.app.plugins.plugins["smart-connections"] && 
            window.app.plugins.plugins["smart-connections"].env) {
            
            const api = window.app.plugins.plugins["smart-connections"].env;
            // Cache for consistency
            window.SmartSearch = api;
            return api;
        }
    } catch (e) {
        console.warn("Smart Connections fallback detection failed:", e);
    }
    
    return null;
}

// Use enhanced detection
window.SmartSearch = detectSmartSearch() || window.SmartSearch`;
        
        // Insert the enhanced detection near the beginning of the file
        // Look for a good insertion point (after initial setup)
        let insertionPoint = fixedContent.indexOf('window.SmartSearch');
        
        if (insertionPoint === -1) {
            // If we can't find window.SmartSearch, insert after the first function or var declaration
            insertionPoint = Math.max(
                fixedContent.indexOf('function'),
                fixedContent.indexOf('var '),
                fixedContent.indexOf('let '),
                fixedContent.indexOf('const '),
                1000 // fallback position
            );
        }
        
        if (insertionPoint > 0) {
            fixedContent = fixedContent.slice(0, insertionPoint) + 
                          enhancedDetection + '\n\n' + 
                          fixedContent.slice(insertionPoint);
            
            console.log("✅ Applied enhanced Smart Connections detection fix");
        } else {
            // Fallback: append to end
            fixedContent += '\n' + enhancedDetection;
            console.log("✅ Applied fix at end of file (fallback method)");
        }
        
        // Write the fixed content
        fs.writeFileSync(MCP_TOOLS_PATH, fixedContent);
        console.log("💾 Saved fixed file");
        
        console.log("🎉 Smart Connections detection fix applied successfully!");
        console.log("");
        console.log("📋 Next steps:");
        console.log("1. Restart Obsidian to load the fixed plugin");
        console.log("2. Check if Smart Connections now shows as ✅ detected");
        console.log("3. Test semantic search functionality");
        console.log("");
        console.log(`📁 Backup saved at: ${BACKUP_PATH}`);
        
        return true;
        
    } catch (error) {
        console.error("❌ Error applying fix:", error.message);
        
        // Restore backup if it exists
        if (fs.existsSync(BACKUP_PATH)) {
            try {
                const backupContent = fs.readFileSync(BACKUP_PATH, 'utf8');
                fs.writeFileSync(MCP_TOOLS_PATH, backupContent);
                console.log("🔄 Restored original file from backup");
            } catch (restoreError) {
                console.error("❌ Failed to restore backup:", restoreError.message);
            }
        }
        
        return false;
    }
}

/**
 * Verify the fix was applied
 */
function verifyFix() {
    try {
        const content = fs.readFileSync(MCP_TOOLS_PATH, 'utf8');
        const hasEnhancedDetection = content.includes('detectSmartSearch') && 
                                   content.includes('app.plugins.plugins["smart-connections"]');
        
        if (hasEnhancedDetection) {
            console.log("✅ Fix verification: Enhanced detection code found in file");
            return true;
        } else {
            console.log("❌ Fix verification: Enhanced detection code not found");
            return false;
        }
    } catch (error) {
        console.error("❌ Error verifying fix:", error.message);
        return false;
    }
}

// Main execution
if (require.main === module) {
    console.log("🚀 Smart Connections Detection Fix for macOS");
    console.log("Based on PR #23: Fix linux smart connections detection");
    console.log("");
    
    const success = applySmartConnectionsFix();
    
    if (success) {
        console.log("🔍 Verifying fix...");
        verifyFix();
    }
}

module.exports = {
    applySmartConnectionsFix,
    verifyFix
}; 
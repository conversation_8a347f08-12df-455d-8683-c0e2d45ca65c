const fs = require('fs');
const path = require('path');

// =================================================================================
// THE ONLY THING YOU SHOULD NEED TO CHANGE
// =================================================================================
const vaultPath = '/Users/<USER>/Library/CloudStorage/OneDrive-COOBLINKINC/obsidian-projects';
// =================================================================================

const pluginPath = path.join(vaultPath, '.obsidian', 'plugins', 'mcp-tools');

const manifest = {
  "id": "mcp-tools",
  "name": "MCP Tools",
  "version": "1.1.0-fix-1",
  "minAppVersion": "1.5.12",
  "description": "Brings the power of community tools to any AI assistant.",
  "author": "Obsidian MCP",
  "authorUrl": "https://github.com/obsidian-mcp",
  "isDesktopOnly": true,
  "fundingUrl": "https://github.com/sponsors/obsidian-mcp"
};

const mainJsBase64 = '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';

const mainJsContent = Buffer.from(mainJsBase64, 'base64').toString('utf-8');

try {
  if (!fs.existsSync(pluginPath)) {
    console.log(`Plugin directory does not exist. Creating: ${pluginPath}`);
    fs.mkdirSync(pluginPath, { recursive: true });
  }

  const mainJsPath = path.join(pluginPath, 'main.js');
  const manifestPath = path.join(pluginPath, 'manifest.json');

  console.log(`Writing main.js to: ${mainJsPath}`);
  fs.writeFileSync(mainJsPath, mainJsContent);

  console.log(`Writing manifest.json to: ${manifestPath}`);
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));

  console.log('\n✅ Fix applied successfully!');
  console.log('Please restart Obsidian to apply the changes.');

} catch (error) {
  console.error('❌ An error occurred while applying the fix:');
  console.error(error);
  console.log('\nPlease ensure the vault path is correct and you have write permissions.');
} 
{"type": "function", "function": {"name": "lookup", "description": "Common, frequently used. Performs a semantic search of the user's data. Use to respond to 'Based on my notes...' or any other query that might require surfacing unspecified content.", "parameters": {"type": "object", "properties": {"hypotheticals": {"type": "array", "description": "Short hypothetical notes predicted to be semantically similar to the notes necessary to fulfill the user's request. At least three hypotheticals per request. The hypothetical notes may contain paragraphs, lists, or checklists in markdown format. Hypothetical notes always begin with breadcrumbs containing the anticipated folder(s), file name, and relevant headings separated by ' > ' (no slashes). Example: PARENT FOLDER NAME > CHILD FOLDER NAME > FILE NAME > HEADING 1 > HEADING 2 > HEADING 3: HYPOTHETICAL NOTE CONTENTS.", "items": {"type": "string"}}}}}}
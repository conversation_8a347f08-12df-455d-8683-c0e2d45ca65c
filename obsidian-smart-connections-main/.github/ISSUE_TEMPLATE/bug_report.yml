name: "Bug Report"
description: "Report a bug with the Smart Connections plugin"
title: "[Bug]: <short description>"
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        ## Bug Description
        Thank you for taking the time to report a bug 😊
        Please fill out the form below as completely as possible to help resolve the issue quickly.
        *Console logs: Please do not "copy & paste" console logs. Instead, please screenshot relevant portions of the logs.*
  - type: textarea
    id: description
    attributes:
      label: Description
      description: |
        Please provide a clear and concise description of the problem.
        Tip: Include what you were trying to do and what went wrong.
      placeholder: "e.g. When I enable Smart Connections, Obsidian freezes."
    validations:
      required: true
  - type: textarea
    id: steps
    attributes:
      label: Steps to Reproduce
      description: |
        List the steps to reproduce the issue, if possible.
        Tip: Number each step for clarity.
      placeholder: |
        1. Open Obsidian
        2. Enable Smart Connections
        3. Click 'Connect'
    validations:
      required: false
  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: What did you expect to happen?
      placeholder: "e.g. The plugin should connect without freezing."
    validations:
      required: false
  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: What actually happened?
      placeholder: "e.g. Obsidian became unresponsive after clicking 'Connect'."
    validations:
      required: false
  - type: textarea
    id: logs
    attributes:
      label: Screenshots
      description: |
        Screenshots are often very helpful for debugging issues (console logs, settings, etc.)
        Put "NA" if not applicable.
    validations:
      required: true 
  - type: markdown
    attributes:
      value: |
        ---
        ### Environment Information
        Please provide details about your setup. This helps us reproduce and fix the issue.
  - type: input
    id: os
    attributes:
      label: Operating System
      placeholder: "e.g. Windows 11, macOS 13.4, Ubuntu 22.04"
    validations:
      required: false
  - type: input
    id: obsidian_version
    attributes:
      label: Obsidian Version
      placeholder: "e.g. 1.8.10"
    validations:
      required: false
  - type: input
    id: plugin_version
    attributes:
      label: Smart Connections Version
      placeholder: "e.g. 3.0.42"
    validations:
      required: false
  - type: input
    id: ai_provider
    attributes:
      label: AI Provider/Model
      placeholder: "e.g. OpenAI GPT-4, Ollama – Llama2 7B"
    validations:
      required: false
  - type: textarea
    id: other_plugins
    attributes:
      label: Other Enabled Plugins
      description: List other enabled plugins, or type "none" if no others are enabled.
      placeholder: "e.g. Calendar, Dataview, or \"none\""
    validations:
      required: false
  - type: textarea
    id: context
    attributes:
      label: Additional Context
      description: Any other relevant information that might help us understand the issue.
      placeholder: "e.g. This only happens after a recent update."
    validations:
      required: false

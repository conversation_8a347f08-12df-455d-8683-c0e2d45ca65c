# Smart Connections `v3`
## New Features

### Smart Chat v1
- Effectively utilizes the Smart Environment architecture to facilitate deeper integration and new features.
#### Improved Smart Chat UI
- New context builder
	- makes managing conversation context easier
- Drag images and notes into the chat window to add as context
- Separate settings tab specifically for chat features
#### *Improved Smart Chat compatibility with Local Models*
- Note lookup (RAG) now compatible with models that don't support tool calling
	- Disable tool calling in the settings
### Ollama embedding adapter
- use Ollama to create embeddings

## Fixed
- renders content in connections results when all result items are expanded by default
## Housekeeping
- Updated README
	- Improved Getting Started section
	- Removed extraneous details
- Improved version release process
- Smart Chat `v0` (legacy)
	- Smart Chat `v0` will continue to be available for a short time and will be removed in `v3.1` unless unforeseen issues arise in which case it will be removed sooner.
	- Smart Chat `v0` code was moved from `brianpetro/jsbrains` to the Smart Connections repo

## patch `v3.0.1`

Improved Mobile UX and cleaned up extraneous code.

## patch `v3.0.3`

Fixed issue where connections results would not render if expand-all results was toggled on.

## patch `v3.0.4`

Prevented frontmatter blocks from being included in connections results. Fixed toggle-fold-all logic.

## patch `v3.0.5`

Fixes Ollama Embedding model loading issue in the settings.

## patch `v3.0.6`

Fixed release notes should only show once after update.

## patch `v3.0.7`

Added "current/dynamic" option in bases connection score modal to add score based on current file. Fixed issue causing Ollama to seemingly embed at 0 tokens/sec. Fixed bases integration modal failing on new bases.

## patch `v3.0.8`

- Improved bases integration UX
	- prevent throwing error on erroroneous input in `cos_sim` base function
	- gracefully handle when smart_env is not loaded yet
- Reduced max size of markdown file that will be imported from 1MB to 300KB (prevent long initial import)
	- advanced configuration available via `smart_sources.obsidian_markdown_source_content_adapter.max_import_size` in `smart_env.json`
- Removed deprecated Smart Search API registered to window since `smart_env` object is now globally accessible
- Fixed bug causing expanded connections results to render twice

## patch `v3.0.9`

- Reworked the context builder UX in Smart Chat to prevent confusion
	- Context is now added to the chat regardless of how the context selector modal is closed
	- Removed "Back" button in favor of "Back" suggestion item
- Fixed using `@` to open context selector in Smart Chat
	- "Done" button now appears in the context selector modal when it is opened from the keyboard

## patch `v3.0.10`

Fixed Google Gemini integration in the new Smart Chat

## patch `v3.0.11`

Fixes unexpected scroll issue when dragging file from connections view (issue #1073)

## patch `v3.0.12`

Fixes pasted text: should paste lines in correct order (no longer reversed)

## patch `v3.0.13`

- Prevents trying to process embed queue if embed model is not loaded
	- Particularly for Ollama which may not be turned on when Obsidian starts
	- Re-checks for Ollama server in intervals of a minute
	- Embed queue can be restarted by clicking "Reload sources" in the Smart Environment settings

## patch `v3.0.14`

- Improved hover popover for blocks in connections results and context builder
- Refactored `context_builder` component to extract `context_tree` component and prevent passing UI components
  - these components are frequently re-used, the updated architecture should make it easier to maintain and extend
- Fixed: should not embed blocks with size less than `min_chars`
- Fixed: Smart Chat completion requests should have a properly ordered `messages` array

## patch `v3.0.15`

- Fixed: some Ollama embedding models triggering re-embedding every restart

## patch `v3.0.16`

- Fixed: no models available in Ollama should no longer cause issues in the settings

## patch `v3.0.17`

- Improved embedding processing UX
	- show notification immediately to allow pausing sooner
	- show notification every 30 seconds in addition to every 100 embeddings
- Fixed: Smart Environment settings tab should be visible during "loading" state
	- prevents "Loading Obsidian Smart Environment..." message from appearing indefinitely in instances where the environment fails to load from errors related to specific embedding models

## patch `v3.0.18`

- Fixed: Smart Connections view rendering on mobile
	- should render when opening the view from the sidebar
	- should update the results to the currently active file

## patch `v3.0.19`

- Added: model info to Smart Chat view
	- shows before the first message and anytime the model changes since the last message
- Fixed: ChatGPT sign-in with Google account
	- should now work as expected
	- will require re-signing in to ChatGPT after update
- Fixed: Smart Chat thread adapter should better handle past completions to prevent unexpected behavior
	- prevented `build_request` from outputting certain request content unless the completion is the current completion
		- logic is specific to completion adapters (actions, actions_xml, thread)

## patch `v3.0.20`

- Fixed: Smart Environment settings tab should be visible during "loading" and "loaded" states
- Fixed: Open URL externally should use window.open with "_external" if webviewer plugin is installed

## patch `v3.0.21`

- Implemented Smart Completions fallback to Smart Chat configuration
	- WHY: enables use via global `smart_env` instance without requiring `chat_model` parameters in every request

## patch `v3.0.22`

- Improved connections view event handling
	- prevent throwing error when no view container is present on iOS

## patch `v3.0.23`

- Added Getting Started guide
	- opens automatically for new users
	- can be opened manually via command `Show getting started`
	- can be opened from the connections view "Help" icon
	- can be opened from the main settings "Open getting started guide" button

## patch `v3.0.24`

Fix Lookup tab not displaying.

## patch `v3.0.25`

Fixed connections view help button failing to open

## patch `v3.0.26`

Temp disable bases integration since Obsidian changed how the integration works and there is currently no clear path to updating.

## patch `v3.0.27`

- Added: Smart Chat lookup now supports folder-based filtering
	- mention a folder when requesting a lookup using self-referential pronoun (no special folder syntax required)
		- ex. "Summarize my thoughts on this topic based on notes in my Content folder"
- Added: Smart Chat system prompt now allows `{{folder_tree}}` variable
	- this variable will be replaced with the folder tree of the current vault
	- useful for providing context about the vault structure to the model
- Improved: Smart Chat system message UI
	- now collapses when longer than 10 lines

## patch `v3.0.28`

Fixed: Getting Started slideshow UX on mobile.

## patch `v3.0.29`

- Fixed: prevented regex special characters from throwing error when excluded file/folder contains them
- Fixed: Smart Chat should return lookup context results when Smart Blocks are disabled

## patch `v3.0.30`

- Added: Drag multiple files into the Smart Chat window to add as context
- Fixed: Smart Connections results remain stable when dragging connection from bottom of the list

## patch `v3.0.31`

- Added: Smart Chat: "Retrieve more" button in lookup results
	- allows retrieving more results from the lookup
	- includes retrieved context in subsequent lookup to provide more context to the model
- Improved: Smart Chat: prior message handling in subsequent completions

## patch `v3.0.32`

- Added: Anthropic Claude Sonnet 4 & Opus 4 to Smart Chat
- Improved: Smart Chat new note button no longer automatically addes open notes as context 
	- Added: "Add visible" and "Add open" notes options to Smart Context selector 
	- Added: "Add context" button above chat input on new chat for quick access to context selector
- Fixed: Removing an item in the context selector updates the stats
- Fixed: Smart Chat system message should render no more than once per turn

## patch `v3.0.33`

- Improved: Context Tree styles improved by samhiatt (PR #1091)
- Improved: Smart Chat message should be full width if container is less than 600px
- Fixed: Smart Chat model selection should handle when Ollama is available but no models are installed

## patch `v3.0.34`

- Added: Multi-modal support (images as context) using Ollama models
	- requires Ollama models that support multi-modal input like `gemma3:4b`


## patch `v3.0.37`

- Fixed: Ollama `max_tokens` parameter should accurately reflect the model's max tokens
- Fixed: Getting Started slideshow should only show automatically for new users

## patch `v3.0.38`

- Fixed: Smart Chat LM Studio models handling of `tool_choice` parameter

## patch `v3.0.39`

- Improved: Release notes user experience to use the same as the native Obsidian release notes
	- Now uses new tab instead of modal to display the release notes
- Fixed: Reduced vector length OpenAI embedding models should be selectable in the settings

## patch `v3.0.40`

- Added: Smart Chat: Support for PDFs as context in compatible models
	- Currently works with Anthropic, Google Gemini, and OpenAI models
	- PDFs must be manually added to the chat context. The context lookup action will not surface the PDFs because they are not embedded.
- Improved: Smart Chat: LM Studio settings
	- Added: Instructions for setting up LM Studio (CORS)
	- Removed: Unecessary API key setting

## patch `v3.0.41`

- Fix: Bug in outlinks parsing was preventing embedding processing in some cases

## patch `v3.0.42`

- Added: `re_import_wait_time` setting to Smart Environment settings
	- allows setting the time to wait before re-importing and embedding a note after it has been modified
	- WHY: improves real-time nature of the connections
- Improved: Connections view: Handling when current note hasn't been imported
	- removed notification
	- added refresh instructions to the connections view
- Improved: Connections view when no results are found
 - added "No connections found" message
 - added instructions for reloading sources from the settings
- Reduced size of bundled plugin from ~6.5MB to ~1MB (>80% reduction)
 - removed tokenizer that's only used by OpenAI embedding models
 - removed sourcemap since it's removed by Obsidian anyway
 - WHY: make the code easier to read (trust through transparency)
- Fixed: Embeddings should update when file is changed

## patch `v3.0.43`

- Fixed: Smart Chat: Context tree connections icon should show connections in the suggestions when clicked

## patch `v3.0.44`

- Improved: Settings descriptions for the Connections view
- Changed: Moved "muted notices" settings to the obsidian-smart-env module

## patch `v3.0.45`

- Added: Status element for indicating embedding queue for changed notes
	- click to begin embedding otherwise waits until `re_import_wait_time` has passed
- Fixed: Smart Environment: only changed blocks should re-embed when the note is modified
	- Adds block has check to parse_blocks to prevent `queue_embed` from being called on blocks that haven't changed
- Fixed: Release notes should open in a new tab instead of relpacing the current tab
- Moved: Smart Plugins access to the obsidian-smart-env module

## patch `v3.0.46`

- Added: Smart Chat: Include relevance score for item in context tree if retrieved from a lookup
	- allows users to see how relevant the item is to the current chat context
- Added: Snowflake Arctic Embed models to the built-in embedding adapter (transformers)
	- Snowflake/snowflake-arctic-embed-xs
	- Snowflake/snowflake-arctic-embed-s
	- Snowflake/snowflake-arctic-embed-m
- Added: Report a bug and Request a feature buttons to the settings
- Fixed: Smart Context: Tree should not split paths with slashes or hashtags within wikilinks
	- ex. `[[some/path.md#subpath]]` should not be split into `some/path.md` and `subpath`
- Improved: Smart Chat: Prevent trying to use folder scope in lookup when the folder provided by the AI does not exist

## patch `v3.0.47`

- Added: Hide connections in connections view
	- Right-click on a connection result to open the new context menu
	- Select "Hide" to hide the connection result
	- Select "Unhide All" to unhide all hidden connections for the current item
- Updated: Smart Contexts to use new ContextItem architecture
	- The new architecture allows for more flexibility and better performance

## patch `v3.0.50`

- Added: Smart Chat: Latest OpenAI chat models (removed incompatible models)
	- o3 and o4 class models now available in the settingsa

## patch `v3.0.51`

- Fixed: Connections view: Include/Exclude filters should allow multiple comma-separated values

## patch `v3.0.52`

- Fixed: Initial import should not embed blocks where `should_embed` is false
  - see #1077 for details
	- improves performance and decreases embedding time by reducing total number of blocks
	- may require "Clear sources data" and "Reload sources" to be run in the settings to take effect
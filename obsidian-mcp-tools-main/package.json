{"name": "mcp-tools-for-obsidian", "version": "0.2.22", "private": true, "description": "Securely connect <PERSON> to your Obsidian vault with semantic search, templates, and file management capabilities.", "tags": ["obsidian", "plugin", "semantic search", "templates", "file management", "mcp", "model context protocol"], "workspaces": ["packages/*"], "scripts": {"check": "bun --filter '*' check", "dev": "bun --filter '*' dev", "version": "bun scripts/version.ts", "release": "bun --filter '*' release", "zip": "bun --filter '*' zip"}, "devDependencies": {"npm-run-all": "^4.1.5"}, "patchedDependencies": {"svelte@5.16.0": "patches/<EMAIL>"}}
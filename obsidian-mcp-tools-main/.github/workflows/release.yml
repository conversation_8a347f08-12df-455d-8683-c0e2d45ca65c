name: Release

on:
  push:
    tags:
      - "*"
    branches:
      - main

jobs:
  release:
    if: github.ref_type == 'tag'
    runs-on: ubuntu-latest
    permissions:
      contents: write
      id-token: write
      attestations: write
    steps:
      - uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Create Release
        id: create_release
        uses: softprops/action-gh-release@v1
        with:
          generate_release_notes: true
          draft: false
          prerelease: false

      - name: Install Dependencies
        run: bun install --frozen-lockfile

      - name: Run Release Script
        env:
          GITHUB_DOWNLOAD_URL: ${{ github.server_url }}/${{ github.repository }}/releases/download/${{ github.ref_name }}
          GITHUB_REF_NAME: ${{ github.ref_name }}
        run: bun run release

      - name: Zip Release Artifacts
        run: bun run zip

      - name: Generate artifact attestation for MCP server binaries
        uses: actions/attest-build-provenance@v2
        with:
          subject-path: "packages/mcp-server/dist/*"

      - name: Get existing release body
        id: get_release_body
        uses: actions/github-script@v7
        with:
          result-encoding: string # This tells the action to return a raw string
          script: |
            const release = await github.rest.repos.getRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: ${{ steps.create_release.outputs.id }}
            });
            return release.data.body || '';

      - name: Upload Release Artifacts
        env:
          GH_WORKFLOW_URL: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
        uses: ncipollo/release-action@v1
        with:
          allowUpdates: true
          omitName: true
          tag: ${{ github.ref_name }}
          artifacts: "packages/obsidian-plugin/releases/obsidian-plugin-*.zip,main.js,manifest.json,styles.css,packages/mcp-server/dist/*"
          body: |
            ${{ steps.get_release_body.outputs.result }}

            ---
            ✨ This release includes attested build artifacts.
            📝 View attestation details in the [workflow run](${{ env.GH_WORKFLOW_URL }})

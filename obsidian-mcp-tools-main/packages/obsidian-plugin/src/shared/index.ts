import type { App } from "obsidian";
import { getAPI, LocalRestApiPublicApi } from "obsidian-local-rest-api";
import {
  distinct,
  interval,
  map,
  merge,
  scan,
  startWith,
  takeUntil,
  takeWhile,
  timer,
} from "rxjs";
import type { SmartConnections, Templater } from "shared";
import type McpToolsPlugin from "src/main";

export interface Dependency<ID extends keyof App["plugins"]["plugins"], API> {
  id: keyof Dependencies;
  name: string;
  required: boolean;
  installed: boolean;
  url?: string;
  api?: API;
  plugin?: App["plugins"]["plugins"][ID];
}

export interface Dependencies {
  "obsidian-local-rest-api": Dependency<
    "obsidian-local-rest-api",
    LocalRestApiPublicApi
  >;
  "smart-connections": Dependency<
    "smart-connections",
    SmartConnections.SmartSearch
  >;
  "templater-obsidian": Dependency<"templater-obsidian", Templater.ITemplater>;
}

// Smart Connections v3.0 API structure
declare const window: {
  SmartSearch?: SmartConnections.SmartSearch;
  smart_env?: {
    smart_sources?: {
      lookup?: (opts: { hypotheticals: string[] }) => Promise<any[]>;
      items?: Record<string, any>;
    };
    state?: string;
  };
} & Window;

export const loadSmartSearchAPI = (plugin: McpToolsPlugin) =>
  interval(200).pipe(
    takeUntil(timer(5000)),
    map((): Dependencies["smart-connections"] => {
      // Smart Connections v3.0 compatibility
      let api: SmartConnections.SmartSearch | undefined;
      
      // Check for v3.0 API first (window.smart_env)
      if (window.smart_env?.smart_sources?.lookup && window.smart_env.state === 'loaded') {
        const smartEnv = window.smart_env;
        
        // Create compatibility wrapper for v3.0
        api = {
          search: async (query: string, filter?: any) => {
            try {
              // Use v3.0 lookup method
              if (!smartEnv.smart_sources?.lookup) {
                throw new Error("Smart Connections v3.0 lookup method not available");
              }
              const results = await smartEnv.smart_sources.lookup({ 
                hypotheticals: [query] 
              });
              
              // Transform v3.0 results to match v2.x format
              return results.map((result: any) => ({
                item: {
                  path: result.item?.path || result.path,
                  data: result.item?.data || { text: result.text },
                  breadcrumbs: result.item?.breadcrumbs || result.breadcrumbs || '',
                  read: async () => {
                    // Try to get text from various possible locations
                    if (result.item?.read && typeof result.item.read === 'function') {
                      return await result.item.read();
                    }
                    return result.item?.data?.text || result.text || '[Content unavailable]';
                  }
                },
                score: result.score || 0
              }));
            } catch (error) {
              console.error('Smart Connections v3.0 search error:', error);
              throw error;
            }
          }
        } as SmartConnections.SmartSearch;
        
        // Cache the compatibility wrapper
        window.SmartSearch = api;
      }
      
      // Fallback to v2.x API detection
      if (!api) {
        // Try direct window.SmartSearch (v2.x)
        if (window.SmartSearch) {
          api = window.SmartSearch;
        } else {
          // Try the PR #23 fallback for v2.x
          const scPlugin = plugin.app.plugins.plugins["smart-connections"];
          if (scPlugin && "env" in scPlugin) {
            window.SmartSearch = scPlugin.env as SmartConnections.SmartSearch;
            api = window.SmartSearch;
          }
        }
      }

      return {
        id: "smart-connections",
        name: "Smart Connections",
        required: false,
        installed: !!api,
        api,
        plugin: plugin.app.plugins.plugins["smart-connections"],
      };
    }),
    takeWhile((dependency) => !dependency.installed, true),
    distinct(({ installed }) => installed),
  );

export const loadLocalRestAPI = (plugin: McpToolsPlugin) =>
  interval(200).pipe(
    takeUntil(timer(5000)),
    map((): Dependencies["obsidian-local-rest-api"] => {
      const api = getAPI(plugin.app, plugin.manifest);
      return {
        id: "obsidian-local-rest-api",
        name: "Local REST API",
        required: true,
        installed: !!api,
        api,
        plugin: plugin.app.plugins.plugins["obsidian-local-rest-api"],
      };
    }),
    takeWhile((dependency) => !dependency.installed, true),
    distinct(({ installed }) => installed),
  );

export const loadTemplaterAPI = (plugin: McpToolsPlugin) =>
  interval(200).pipe(
    takeUntil(timer(5000)),
    map((): Dependencies["templater-obsidian"] => {
      const api = plugin.app.plugins.plugins["templater-obsidian"]?.templater;
      return {
        id: "templater-obsidian",
        name: "Templater",
        required: false,
        installed: !!api,
        api,
        plugin: plugin.app.plugins.plugins["templater-obsidian"],
      };
    }),
    takeWhile((dependency) => !dependency.installed, true),
    distinct(({ installed }) => installed),
  );

export const loadDependencies = (plugin: McpToolsPlugin) => {
  const dependencies: Dependencies = {
    "obsidian-local-rest-api": {
      id: "obsidian-local-rest-api",
      name: "Local REST API",
      required: true,
      installed: false,
      url: "https://github.com/coddingtonbear/obsidian-local-rest-api",
    },
    "smart-connections": {
      id: "smart-connections",
      name: "Smart Connections",
      required: false,
      installed: false,
      url: "https://smartconnections.app/",
    },
    "templater-obsidian": {
      id: "templater-obsidian",
      name: "Templater",
      required: false,
      installed: false,
      url: "https://silentvoid13.github.io/Templater/",
    },
  };
  return merge(
    loadLocalRestAPI(plugin),
    loadTemplaterAPI(plugin),
    loadSmartSearchAPI(plugin),
  ).pipe(
    scan((acc, dependency) => {
      // @ts-expect-error Dynamic key assignment
      acc[dependency.id] = {
        ...dependencies[dependency.id],
        ...dependency,
      };
      return acc;
    }, dependencies),
    startWith(dependencies),
  );
};

export const loadDependenciesArray = (plugin: McpToolsPlugin) =>
  loadDependencies(plugin).pipe(
    map((deps) => Object.values(deps) as Dependencies[keyof Dependencies][]),
  );

export * from "./logger";


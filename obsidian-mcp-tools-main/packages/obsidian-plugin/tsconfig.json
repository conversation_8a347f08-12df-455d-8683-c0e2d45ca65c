{"compilerOptions": {"baseUrl": ".", "inlineSourceMap": true, "inlineSources": true, "module": "ESNext", "target": "ES2018", "allowJs": true, "noEmit": true, "noImplicitAny": true, "moduleResolution": "bundler", "importHelpers": true, "isolatedModules": true, "strict": true, "skipLibCheck": true, "lib": ["DOM", "ES5", "ES6", "ES7"], "useDefineForClassFields": true, "verbatimModuleSyntax": true, "paths": {"$/*": ["./src/*"]}}, "include": ["src/*.ts", "bun.config.ts"], "exclude": ["node_modules", "playground"]}
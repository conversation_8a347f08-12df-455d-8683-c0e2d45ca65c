// Smart Connections Detection Fix for macOS
// Based on PR #23: Fix linux smart connections detection
// This fix adds a fallback detection mechanism for Smart Connections plugin

/**
 * Enhanced Smart Connections API detection function
 * This function implements the fix from PR #23 to detect Smart Connections
 * on macOS systems where window.SmartSearch might not be consistently available
 */
function loadSmartSearchAPI() {
    // First try the standard window.SmartSearch (works on some platforms)
    if (window.SmartSearch) {
        console.log("✅ Smart Connections detected via window.SmartSearch");
        return window.SmartSearch;
    }
    
    // Fallback: Check app.plugins.plugins["smart-connections"].env (fixes macOS/Linux)
    try {
        if (app && app.plugins && app.plugins.plugins && app.plugins.plugins["smart-connections"]) {
            const smartConnectionsPlugin = app.plugins.plugins["smart-connections"];
            
            if (smartConnectionsPlugin.env) {
                console.log("✅ Smart Connections detected via app.plugins fallback");
                
                // Cache the API reference in window.SmartSearch for consistency
                window.SmartSearch = smartConnectionsPlugin.env;
                
                return smartConnectionsPlugin.env;
            }
        }
    } catch (error) {
        console.warn("❌ Error checking Smart Connections via app.plugins:", error);
    }
    
    console.warn("❌ Smart Connections not detected via any method");
    return null;
}

/**
 * Test function to verify Smart Connections detection
 */
function testSmartConnectionsDetection() {
    console.log("🔍 Testing Smart Connections detection...");
    
    const api = loadSmartSearchAPI();
    
    if (api) {
        console.log("🎉 SUCCESS: Smart Connections API detected and available!");
        console.log("API methods available:", Object.keys(api));
        return true;
    } else {
        console.log("❌ FAILED: Smart Connections API not detected");
        return false;
    }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        loadSmartSearchAPI,
        testSmartConnectionsDetection
    };
}

// Auto-run test when loaded in browser context
if (typeof window !== 'undefined') {
    console.log("🚀 Smart Connections Detection Fix loaded");
    // Uncomment the next line to auto-test on load
    // setTimeout(testSmartConnectionsDetection, 1000);
} 
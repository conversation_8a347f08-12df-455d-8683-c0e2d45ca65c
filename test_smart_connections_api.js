// Test script to run in Obsidian Developer Console
// This will help us understand how Smart Connections API is exposed

console.log("Testing Smart Connections API...");

// Test 1: Check if Smart Connections plugin is loaded
const smartConnectionsPlugin = app.plugins.plugins["smart-connections"];
console.log("Smart Connections plugin:", smartConnectionsPlugin);

// Test 2: Check if env property exists
if (smartConnectionsPlugin && smartConnectionsPlugin.env) {
    console.log("Smart Connections env found:", smartConnectionsPlugin.env);
    
    // Test 3: Check if search API is available
    if (smartConnectionsPlugin.env.smart_sources) {
        console.log("smart_sources found:", smartConnectionsPlugin.env.smart_sources);
        
        // Test 4: Try to perform a search
        try {
            const results = await smartConnectionsPlugin.env.smart_sources.search("steel", {limit: 5});
            console.log("Search results:", results);
        } catch (error) {
            console.error("Search error:", error);
        }
    } else {
        console.log("smart_sources not found in env");
    }
} else {
    console.log("Smart Connections env not found");
}

// Test 5: Check window.SmartSearch
if (window.SmartSearch) {
    console.log("window.SmartSearch found:", window.SmartSearch);
} else {
    console.log("window.SmartSearch not found");
}

// Test 6: Check all available properties
if (smartConnectionsPlugin) {
    console.log("Smart Connections plugin properties:", Object.keys(smartConnectionsPlugin));
} 
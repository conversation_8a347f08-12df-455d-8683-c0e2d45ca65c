const { build } = require('bun');
const fs = require('fs/promises');
const path = require('path');

async function runBuild() {
  console.log('Starting custom build...');

  const pluginDir = path.join(process.cwd(), 'packages', 'obsidian-plugin');
  const sharedIndexPath = path.join(pluginDir, 'src', 'shared', 'index.ts');
  const mainTsPath = path.join(pluginDir, 'src', 'main.ts');
  const targetDir = '/Users/<USER>/Library/CloudStorage/OneDrive-COOBLINKINC/obsidian-projects/.obsidian/plugins/mcp-tools/';

  // --- Apply Smart Connections Fix ---
  console.log('Applying Smart Connections fix to shared/index.ts...');
  let sharedIndexContent = await fs.readFile(sharedIndexPath, 'utf-8');
  const fix = `
      if (!window.SmartSearch) {
        const scPlugin = plugin.app.plugins.plugins["smart-connections"];
        if (scPlugin && "env" in scPlugin) {
          window.SmartSearch = scPlugin.env as any;
        }
      }
  `;
  const searchApiRegex = /(map\\(\\(\\): Dependencies\\["smart-connections"\\] => \\{)/;
  if (!sharedIndexContent.includes('scPlugin')) {
      sharedIndexContent = sharedIndexContent.replace(searchApiRegex, `$1\\n${fix}`);
      await fs.writeFile(sharedIndexPath, sharedIndexContent);
      console.log('Fix applied successfully.');
  } else {
      console.log('Fix already applied, skipping.');
  }


  // --- Build the plugin ---
  console.log('Building the plugin programmatically...');
  const result = await build({
    entrypoints: [mainTsPath],
    outdir: targetDir,
    minify: true,
    plugins: [
        // Basic svelte plugin placeholder if needed, may need more complex setup
    ],
    external: [
      "obsidian",
    ],
    target: "node",
    format: "cjs",
    naming: {
      entry: "main.js",
    },
  });

  if (!result.success) {
    console.error("Build failed:", result.logs);
    return;
  }

  console.log('Build successful! New main.js created in target directory.');

  // --- Copy manifest.json ---
  console.log('Copying manifest.json...');
  const manifestPath = path.join(pluginDir, 'manifest.json');
  await fs.copyFile(manifestPath, path.join(targetDir, 'manifest.json'));

  console.log('Final fix applied! Please restart Obsidian.');
}

runBuild().catch(console.error); 
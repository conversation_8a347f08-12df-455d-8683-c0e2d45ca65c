// Test script for Smart Connections v3.0 API debugging
// Run this in Obsidian Developer Console

console.log("=== Smart Connections v3.0 API Debug Test ===");

// Test 1: Check if window.smart_env exists
console.log("1. Checking window.smart_env...");
if (window.smart_env) {
  console.log("✅ window.smart_env found");
  console.log("   smart_env keys:", Object.keys(window.smart_env));
  
  // Test 2: Check smart_sources
  console.log("2. Checking smart_sources...");
  if (window.smart_env.smart_sources) {
    console.log("✅ smart_sources found");
    console.log("   smart_sources keys:", Object.keys(window.smart_env.smart_sources));
    console.log("   items count:", Object.keys(window.smart_env.smart_sources.items || {}).length);
    
    // Test 3: Check lookup method
    console.log("3. Checking lookup method...");
    if (window.smart_env.smart_sources.lookup) {
      console.log("✅ lookup method found");
      console.log("   lookup method type:", typeof window.smart_env.smart_sources.lookup);
      
      // Test 4: Try lookup
      console.log("4. Testing lookup...");
      try {
        const testQuery = "steel";
        console.log(`   Testing lookup with query: "${testQuery}"`);
        
        const results = await window.smart_env.smart_sources.lookup({ 
          hypotheticals: [testQuery] 
        });
        
        console.log("✅ Lookup successful!");
        console.log("   Results type:", typeof results);
        console.log("   Results:", results);
        
        if (Array.isArray(results)) {
          console.log("   Results count:", results.length);
          if (results.length > 0) {
            console.log("   First result:", results[0]);
          }
        }
        
      } catch (error) {
        console.error("❌ Lookup failed:", error);
        console.error("   Error details:", error.message);
        console.error("   Stack:", error.stack);
      }
      
    } else {
      console.error("❌ lookup method not found");
      console.log("   Available methods:", Object.keys(window.smart_env.smart_sources).filter(k => typeof window.smart_env.smart_sources[k] === 'function'));
    }
    
  } else {
    console.error("❌ smart_sources not found");
    console.log("   Available properties:", Object.keys(window.smart_env));
  }
  
} else {
  console.error("❌ window.smart_env not found");
  
  // Fallback: Check plugin directly
  console.log("5. Checking plugin directly...");
  const scPlugin = app.plugins.plugins["smart-connections"];
  if (scPlugin) {
    console.log("✅ Smart Connections plugin found");
    console.log("   Plugin keys:", Object.keys(scPlugin));
    
    if (scPlugin.env) {
      console.log("✅ Plugin env found");
      console.log("   Env keys:", Object.keys(scPlugin.env));
      
      // Try to expose to window
      console.log("6. Trying to expose to window...");
      try {
        window.smart_env = scPlugin.env;
        console.log("✅ Successfully exposed to window.smart_env");
      } catch (error) {
        console.error("❌ Failed to expose to window:", error);
      }
    } else {
      console.error("❌ Plugin env not found");
    }
  } else {
    console.error("❌ Smart Connections plugin not found");
  }
}

// Test 7: Check if our MCP Tools wrapper is working
console.log("7. Testing MCP Tools wrapper...");
if (window.SmartSearch) {
  console.log("✅ window.SmartSearch found (MCP Tools wrapper)");
  console.log("   SmartSearch keys:", Object.keys(window.SmartSearch));
  
  if (window.SmartSearch.search) {
    console.log("✅ SmartSearch.search method found");
    try {
      const results = await window.SmartSearch.search("steel", { limit: 5 });
      console.log("✅ SmartSearch.search successful!");
      console.log("   Results:", results);
    } catch (error) {
      console.error("❌ SmartSearch.search failed:", error);
    }
  } else {
    console.error("❌ SmartSearch.search method not found");
  }
} else {
  console.error("❌ window.SmartSearch not found (MCP Tools wrapper not working)");
}

console.log("=== Test Complete ==="); 